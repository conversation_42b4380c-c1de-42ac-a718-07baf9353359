#!/usr/bin/env python3
"""
Final verification test for all critical issues
"""
import requests
import re
import random
import string

BASE_URL = "http://127.0.0.1:8080"

def random_string(length=8):
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def get_csrf_token(html_content):
    """Extract CSRF token from HTML"""
    match = re.search(r'name="csrf_token" value="([^"]+)"', html_content)
    return match.group(1) if match else None

def test_csrf_and_authentication():
    """Test CSRF Token and Authentication Issues"""
    print("🔐 Testing CSRF Token and Authentication")
    print("-" * 40)
    
    session = requests.Session()
    
    # Test 1: Authentication redirects
    protected_routes = ["/plants/list", "/admin", "/profile"]
    for route in protected_routes:
        response = session.get(f"{BASE_URL}{route}", allow_redirects=False)
        if response.status_code == 302 and "/auth/login" in response.headers.get('Location', ''):
            print(f"✅ {route} correctly redirects to login")
        else:
            print(f"❌ {route} redirect failed")
            return False
    
    # Test 2: CSRF tokens in forms
    login_response = session.get(f"{BASE_URL}/auth/login")
    csrf_token = get_csrf_token(login_response.text)
    if csrf_token:
        print("✅ CSRF token present in login form")
    else:
        print("❌ CSRF token missing in login form")
        return False
    
    # Test 3: Successful authentication
    username = f"testuser_{random_string()}"
    password = "testpass123"
    
    # Register
    reg_response = session.get(f"{BASE_URL}/auth/register")
    csrf_token = get_csrf_token(reg_response.text)
    
    register_data = {
        'username': username,
        'password': password,
        'password_confirm': password,
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/auth/register", data=register_data)
    if response.status_code in [200, 302]:
        print("✅ Registration with CSRF token successful")
    else:
        print("❌ Registration failed")
        return False
    
    # Test 4: Authenticated user sees complete navigation
    dashboard_response = session.get(f"{BASE_URL}/")
    if "Plants" in dashboard_response.text and "Seeds" in dashboard_response.text:
        print("✅ Authenticated user sees complete navigation")
    else:
        print("❌ Navigation incomplete for authenticated user")
        return False
    
    return True

def test_material3_design():
    """Test Material 3 Design Quality"""
    print("\n🎨 Testing Material 3 Design Quality")
    print("-" * 40)
    
    session = requests.Session()
    
    # Test pages for Material 3 classes
    test_pages = ["/", "/auth/login", "/auth/register"]
    
    for page in test_pages:
        response = session.get(f"{BASE_URL}{page}")
        content = response.text
        
        # Check for Material 3 classes
        material3_classes = ['material-', 'bg-primary', 'text-surface', 'material-button', 'material-card']
        has_material3 = any(cls in content for cls in material3_classes)
        
        if has_material3:
            print(f"✅ {page} has Material 3 styling")
        else:
            print(f"❌ {page} missing Material 3 styling")
            return False
        
        # Check for proper contrast (no white text on white background issues)
        if 'dark:text-surface-100' in content and 'dark:bg-surface-950' in content:
            print(f"✅ {page} has proper dark theme contrast")
        else:
            print(f"⚠️  {page} may have contrast issues")
    
    return True

def test_admin_crud():
    """Test Admin CRUD Functionality"""
    print("\n🛠️  Testing Admin CRUD Functionality")
    print("-" * 40)
    
    session = requests.Session()
    
    # Login as admin
    login_response = session.get(f"{BASE_URL}/auth/login")
    csrf_token = get_csrf_token(login_response.text)
    
    login_data = {
        'username': 'admin',
        'password': 'admin123',
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code not in [200, 302]:
        print("❌ Admin login failed")
        return False
    
    print("✅ Admin login successful")
    
    # Test HerbaDB management access
    herba_response = session.get(f"{BASE_URL}/admin/herba-db")
    if herba_response.status_code == 200:
        print("✅ HerbaDB management accessible")
    else:
        print("❌ HerbaDB management not accessible")
        return False
    
    content = herba_response.text
    
    # Check for CRUD functionality
    if 'material-button-filled' in content:
        print("✅ Material 3 buttons implemented")
    else:
        print("❌ Material 3 buttons missing")
        return False
    
    if 'csrf_token' in content:
        print("✅ CSRF protection in admin forms")
    else:
        print("❌ CSRF protection missing")
        return False
    
    # Test scraper form
    csrf_token = get_csrf_token(content)
    if csrf_token:
        scrape_data = {
            'plant_name': 'test_plant',
            'csrf_token': csrf_token
        }
        
        scrape_response = session.post(f"{BASE_URL}/admin/herba-db/scrape", data=scrape_data)
        if scrape_response.status_code in [200, 302]:
            print("✅ Plant scraper form functional")
        else:
            print(f"⚠️  Plant scraper returned status: {scrape_response.status_code}")
    
    return True

def main():
    print("🔍 Final Verification Test - Critical Issues")
    print("=" * 50)
    
    # Check server health
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ Server not responding")
            return
    except:
        print("❌ Cannot connect to server")
        return
    
    print("✅ Server is responding")
    
    # Run all tests
    tests = [
        ("CSRF Token and Authentication Issues", test_csrf_and_authentication),
        ("Material 3 Design Quality", test_material3_design),
        ("Admin CRUD Functionality", test_admin_crud)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        if test_func():
            print(f"✅ {test_name} - PASSED")
            passed += 1
        else:
            print(f"❌ {test_name} - FAILED")
    
    print("\n" + "=" * 50)
    print(f"🎯 Final Results: {passed}/{total} critical issues resolved")
    
    if passed == total:
        print("🎉 ALL CRITICAL ISSUES RESOLVED!")
        print("\n✅ CSRF Token and Authentication working correctly")
        print("✅ Material 3 Design implemented with proper contrast")
        print("✅ Admin CRUD functionality operational")
    else:
        print("⚠️  Some issues still need attention")

if __name__ == "__main__":
    main()
