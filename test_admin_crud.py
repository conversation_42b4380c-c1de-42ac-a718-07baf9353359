#!/usr/bin/env python3
"""
Test admin CRUD functionality
"""
import requests
import re
import random
import string

BASE_URL = "http://127.0.0.1:8080"

def random_string(length=8):
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def get_csrf_token(html_content):
    """Extract CSRF token from HTML"""
    match = re.search(r'name="csrf_token" value="([^"]+)"', html_content)
    return match.group(1) if match else None

def test_admin_herba_crud():
    """Test HerbaDB CRUD operations"""
    print("🧪 Testing Admin HerbaDB CRUD Operations")
    print("=" * 50)

    session = requests.Session()

    # Login with existing admin user (first user is superadmin)
    login_response = session.get(f"{BASE_URL}/auth/login")
    csrf_token = get_csrf_token(login_response.text)

    # Try with default admin credentials
    login_data = {
        'username': 'admin',
        'password': 'admin123',
        'csrf_token': csrf_token
    }

    response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code not in [200, 302]:
        print(f"❌ Admin login failed - Status: {response.status_code}")
        print("Trying to create new admin user...")

        # If admin login fails, create a new admin user
        username = f"admin_{random_string()}"
        password = "adminpass123"

        # Register as admin (first user becomes superadmin)
        reg_response = session.get(f"{BASE_URL}/auth/register")
        csrf_token = get_csrf_token(reg_response.text)

        register_data = {
            'username': username,
            'password': password,
            'password_confirm': password,
            'csrf_token': csrf_token
        }

        response = session.post(f"{BASE_URL}/auth/register", data=register_data)
        if response.status_code not in [200, 302]:
            print(f"❌ Admin registration failed - Status: {response.status_code}")
            return False

        print("✅ New admin user created")
    else:
        print("✅ Admin login successful")
    
    # Test access to HerbaDB management
    herba_response = session.get(f"{BASE_URL}/admin/herba-db")
    if herba_response.status_code != 200:
        print(f"❌ Cannot access HerbaDB management - Status: {herba_response.status_code}")
        return False
    
    print("✅ HerbaDB management page accessible")
    
    # Check for edit and delete buttons
    herba_content = herba_response.text
    
    # Check for Material 3 styling
    if 'material-card' in herba_content:
        print("✅ Material 3 styling detected")
    else:
        print("⚠️  Material 3 styling not found")
    
    # Check for functional buttons
    if '/admin/herba-db/' in herba_content and '/edit' in herba_content:
        print("✅ Edit functionality detected")
    else:
        print("⚠️  Edit functionality not found")
    
    if '/admin/herba-db/' in herba_content and '/delete' in herba_content:
        print("✅ Delete functionality detected")
    else:
        print("⚠️  Delete functionality not found")
    
    # Check for CSRF protection
    if 'csrf_token' in herba_content:
        print("✅ CSRF protection detected")
    else:
        print("⚠️  CSRF protection not found")
    
    # Test scraper form
    csrf_token = get_csrf_token(herba_content)
    if csrf_token:
        print("✅ CSRF token available for forms")
        
        # Test plant scraper (with a simple plant name)
        scrape_data = {
            'plant_name': 'basil',
            'csrf_token': csrf_token
        }
        
        scrape_response = session.post(f"{BASE_URL}/admin/herba-db/scrape", data=scrape_data)
        if scrape_response.status_code in [200, 302]:
            print("✅ Plant scraper form submission successful")
        else:
            print(f"⚠️  Plant scraper returned status: {scrape_response.status_code}")
    else:
        print("⚠️  No CSRF token found")
    
    return True

def test_admin_dashboard():
    """Test admin dashboard access"""
    print("\n🏠 Testing Admin Dashboard")
    print("-" * 30)
    
    session = requests.Session()
    
    # Login as admin (reuse existing admin if available)
    login_response = session.get(f"{BASE_URL}/auth/login")
    csrf_token = get_csrf_token(login_response.text)
    
    # Try with default admin credentials
    login_data = {
        'username': 'admin',
        'password': 'admin123',
        'csrf_token': csrf_token
    }
    
    response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code in [200, 302]:
        print("✅ Admin login successful")
        
        # Test admin dashboard
        dashboard_response = session.get(f"{BASE_URL}/admin")
        if dashboard_response.status_code == 200:
            print("✅ Admin dashboard accessible")
            
            # Check for admin features
            dashboard_content = dashboard_response.text
            if 'HerbaDB' in dashboard_content:
                print("✅ HerbaDB management link found")
            if 'Users' in dashboard_content:
                print("✅ User management link found")
        else:
            print(f"⚠️  Admin dashboard status: {dashboard_response.status_code}")
    else:
        print("⚠️  Admin login failed, testing with new user")

def main():
    print("🔧 Garden Planner Admin CRUD Test")
    print("=" * 50)
    
    # Check server health
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ Server not responding")
            return
    except:
        print("❌ Cannot connect to server")
        return
    
    print("✅ Server is responding")
    
    test_admin_dashboard()
    test_admin_herba_crud()
    
    print("\n" + "=" * 50)
    print("🎯 Admin CRUD test completed!")

if __name__ == "__main__":
    main()
