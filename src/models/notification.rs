use crate::models::plant::Plant;
use crate::models::user::User;
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::{NaiveDateTime, NaiveDate};

use crate::schema::notifications;

#[derive(Debug, Clone, Queryable, Identifiable, Associations, Serialize, Deserialize)]
#[diesel(table_name = notifications)]
#[diesel(belongs_to(User))]
#[diesel(belongs_to(Plant))]
pub struct Notification {
    pub id: i32,
    pub user_id: i32,
    pub plant_id: i32,
    pub message: String,
    pub scheduled_time: NaiveDateTime,
    pub sent: bool,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = notifications)]
pub struct NewNotification<'a> {
    pub user_id: i32,
    pub plant_id: i32,
    pub message: &'a str,
    pub scheduled_time: NaiveDateTime,
    pub sent: bool,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = notifications)]
pub struct NewBroadcastNotification<'a> {
    pub user_id: i32,
    pub plant_id: i32, // Use 0 for system-wide notifications
    pub message: &'a str,
    pub scheduled_time: NaiveDateTime,
    pub sent: bool,
}

impl Notification {
    /// Create a new notification
    pub fn create(conn: &mut SqliteConnection, new_notification: &NewNotification) -> QueryResult<Notification> {
        use crate::schema::notifications::dsl::*;

        diesel::insert_into(notifications)
            .values(new_notification)
            .execute(conn)?;

        // Get the last inserted record
        notifications.order(id.desc()).first(conn)
    }

    /// Find notification by ID
    pub fn find_by_id(conn: &mut SqliteConnection, notification_id: i32) -> QueryResult<Option<Notification>> {
        use crate::schema::notifications::dsl::*;
        notifications.find(notification_id).first(conn).optional()
    }

    /// Find all notifications for a user
    pub fn find_for_user(conn: &mut SqliteConnection, user_id_param: i32) -> QueryResult<Vec<Notification>> {
        use crate::schema::notifications::dsl::*;
        notifications
            .filter(user_id.eq(user_id_param))
            .order(scheduled_time.desc())
            .load::<Notification>(conn)
    }

    /// Find unread notifications for a user
    pub fn find_unread_for_user(conn: &mut SqliteConnection, user_id_param: i32) -> QueryResult<Vec<Notification>> {
        use crate::schema::notifications::dsl::*;
        notifications
            .filter(user_id.eq(user_id_param))
            .filter(sent.eq(false))
            .order(scheduled_time.asc())
            .load::<Notification>(conn)
    }

    /// Mark notification as sent
    pub fn mark_as_read(conn: &mut SqliteConnection, notification_id: i32, user_id_param: i32) -> QueryResult<usize> {
        use crate::schema::notifications::dsl::*;

        diesel::update(notifications.filter(id.eq(notification_id).and(user_id.eq(user_id_param))))
            .set(sent.eq(true))
            .execute(conn)
    }

    /// Find overdue notifications for a user
    pub fn find_overdue_for_user(conn: &mut SqliteConnection, user_id_param: i32, today: NaiveDate) -> QueryResult<Vec<Notification>> {
        use crate::schema::notifications::dsl::*;
        let today_datetime = today.and_hms_opt(23, 59, 59).unwrap();

        notifications
            .filter(user_id.eq(user_id_param))
            .filter(sent.eq(false))
            .filter(scheduled_time.lt(today_datetime))
            .order(scheduled_time.asc())
            .load::<Notification>(conn)
    }

    /// Find upcoming notifications for a user
    pub fn find_upcoming_for_user(conn: &mut SqliteConnection, user_id_param: i32, start_date: NaiveDate, end_date: NaiveDate) -> QueryResult<Vec<Notification>> {
        use crate::schema::notifications::dsl::*;
        let start_datetime = start_date.and_hms_opt(0, 0, 0).unwrap();
        let end_datetime = end_date.and_hms_opt(23, 59, 59).unwrap();

        notifications
            .filter(user_id.eq(user_id_param))
            .filter(sent.eq(false))
            .filter(scheduled_time.between(start_datetime, end_datetime))
            .order(scheduled_time.asc())
            .load::<Notification>(conn)
    }

    /// Delete old sent notifications
    pub fn cleanup_old_notifications(conn: &mut SqliteConnection, days_old: i32) -> QueryResult<usize> {
        use crate::schema::notifications::dsl::*;
        let cutoff_date = chrono::Utc::now().naive_utc() - chrono::Duration::days(days_old as i64);

        diesel::delete(notifications.filter(sent.eq(true).and(scheduled_time.lt(cutoff_date))))
            .execute(conn)
    }

    /// Create a broadcast notification for all users
    pub fn create_broadcast(conn: &mut SqliteConnection, message_text: &str) -> QueryResult<Vec<Notification>> {
        use crate::schema::{notifications::dsl::*, users};

        let now = chrono::Utc::now().naive_utc();
        let mut created_notifications = Vec::new();

        // Get all users
        let all_users: Vec<crate::models::user::User> = users::table.load(conn)?;

        for user in all_users {
            let new_notification = NewBroadcastNotification {
                user_id: user.id,
                plant_id: 0, // System-wide notification
                message: message_text,
                scheduled_time: now,
                sent: false,
            };

            diesel::insert_into(notifications)
                .values(&new_notification)
                .execute(conn)?;

            // Get the created notification
            let notification = notifications
                .filter(user_id.eq(user.id))
                .order(id.desc())
                .first(conn)?;

            created_notifications.push(notification);
        }

        Ok(created_notifications)
    }

    /// Create weather alert notification for all users
    pub fn create_weather_alert(conn: &mut SqliteConnection, alert_message: &str, severity: &str) -> QueryResult<Vec<Notification>> {
        let formatted_message = format!("🌦️ Weather Alert ({}): {}", severity.to_uppercase(), alert_message);
        Self::create_broadcast(conn, &formatted_message)
    }
}
