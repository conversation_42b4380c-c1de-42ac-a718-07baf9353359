use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;
use chrono::NaiveDateTime;

use crate::models::{
    notification::{NewNotification, Notification},
    plant::Plant
};
use crate::services::notification_service::NotificationService;
use crate::utils::templates::{render_template, render_template_with_context};
use crate::DbPool;
use crate::schema::{notifications, plants};

#[derive(Deserialize)]
pub struct NotificationForm {
    pub plant_id: i32,
    pub message: String,
    pub scheduled_time: String, // Expected format: "YYYY-MM-DD HH:MM:SS"
}

#[derive(Deserialize)]
pub struct BroadcastForm {
    pub message: String,
}

#[derive(Deserialize)]
pub struct WeatherAlertForm {
    pub message: String,
    pub severity: String,
}

pub async fn list_notifications(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let notifications = notifications::table
        .filter(notifications::user_id.eq(user_id))
        .load::<Notification>(&mut conn)
        .expect("Error loading notifications");

    let mut ctx = tera::Context::new();
    ctx.insert("notifications", &notifications);

    render_template_with_context("notifications/list.html", &mut ctx, &session)
}

pub async fn new_notification_form(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        let plants = plants::table
            .load::<Plant>(&mut conn)
            .expect("Error loading plants");

        let mut ctx = tera::Context::new();
        ctx.insert("plants", &plants);

        render_template("notifications/new.html", &ctx)
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn create_notification(
    session: Session,
    form: web::Form<NotificationForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if let Some(user_id) = session.get::<i32>("user_id")? {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        let scheduled_time = NaiveDateTime::parse_from_str(&form.scheduled_time, "%Y-%m-%d %H:%M:%S")
            .expect("Invalid datetime format");

        let new_notification = NewNotification {
            user_id,
            plant_id: form.plant_id,
            message: &form.message,
            scheduled_time,
            sent: false,
        };

        diesel::insert_into(notifications::table)
            .values(&new_notification)
            .execute(&mut conn)
            .expect("Error inserting new notification");

        Ok(HttpResponse::Found()
            .append_header(("Location", "/notifications/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn recent_notifications_api(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get recent notifications (last 10)
    let recent_notifications = notifications::table
        .filter(notifications::user_id.eq(user_id))
        .order(notifications::scheduled_time.desc())
        .limit(10)
        .load::<Notification>(&mut conn)
        .expect("Error loading notifications");

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "notifications": recent_notifications
    })))
}

pub async fn mark_notification_read(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let notification_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Mark notification as sent (read)
    diesel::update(notifications::table.find(notification_id))
        .filter(notifications::user_id.eq(user_id))
        .set(notifications::sent.eq(true))
        .execute(&mut conn)
        .expect("Error updating notification");

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true
    })))
}

/// Create broadcast notification for all users (admin only)
pub async fn create_broadcast(
    session: Session,
    form: web::Form<BroadcastForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    // Check if user is admin
    let role = session.get::<String>("role")?.unwrap_or_default();
    if role != "admin" && role != "superadmin" {
        return Ok(HttpResponse::Forbidden().json(serde_json::json!({
            "error": "Insufficient permissions"
        })));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    match NotificationService::create_broadcast_notification(&mut conn, &form.message) {
        Ok(notifications) => {
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "message": format!("Broadcast sent to {} users", notifications.len())
            })))
        }
        Err(e) => {
            eprintln!("Error creating broadcast notification: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to create broadcast notification"
            })))
        }
    }
}

/// Create weather alert for all users (admin only)
pub async fn create_weather_alert(
    session: Session,
    form: web::Form<WeatherAlertForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    // Check if user is admin
    let role = session.get::<String>("role")?.unwrap_or_default();
    if role != "admin" && role != "superadmin" {
        return Ok(HttpResponse::Forbidden().json(serde_json::json!({
            "error": "Insufficient permissions"
        })));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    match NotificationService::create_weather_alert(&mut conn, &form.message, &form.severity) {
        Ok(notifications) => {
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "message": format!("Weather alert sent to {} users", notifications.len())
            })))
        }
        Err(e) => {
            eprintln!("Error creating weather alert: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to create weather alert"
            })))
        }
    }
}

/// Generate and schedule care notifications for a user
pub async fn generate_care_notifications(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    match NotificationService::generate_care_notifications(&mut conn, user_id, 7) {
        Ok(notification_requests) => {
            // Schedule the notifications
            match NotificationService::create_notifications(&mut conn, user_id, notification_requests) {
                Ok(notifications) => {
                    Ok(HttpResponse::Ok().json(serde_json::json!({
                        "success": true,
                        "message": format!("Generated {} care notifications", notifications.len()),
                        "notifications": notifications
                    })))
                }
                Err(e) => {
                    eprintln!("Error scheduling notifications: {}", e);
                    Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                        "error": "Failed to schedule notifications"
                    })))
                }
            }
        }
        Err(e) => {
            eprintln!("Error generating care notifications: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to generate care notifications"
            })))
        }
    }
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/notifications")
            .route("/list", web::get().to(list_notifications))
            .route("/new", web::get().to(new_notification_form))
            .route("/create", web::post().to(create_notification))
            .route("/broadcast", web::post().to(create_broadcast))
            .route("/weather_alert", web::post().to(create_weather_alert))
            .route("/generate_care", web::post().to(generate_care_notifications)),
    );

    // Register API routes directly without scope to test
    cfg.route("/api/notifications/recent", web::get().to(recent_notifications_api));
    cfg.route("/api/notifications/{id}/read", web::post().to(mark_notification_read));
}
