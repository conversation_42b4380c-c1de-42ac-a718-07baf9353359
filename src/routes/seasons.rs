use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;
use chrono::NaiveDate;

use crate::models::season::{NewSeason, Season};
use crate::services::season_auto_creator::SeasonAutoCreator;
use crate::utils::templates::{render_template, render_template_with_context};
use crate::DbPool;
use crate::schema::seasons;

#[derive(Deserialize)]
pub struct SeasonForm {
    pub name: String,
    pub start_date: String,
    pub end_date: String,
}

pub async fn list_seasons(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Auto-create seasons for current year if they don't exist
    let season_creator = SeasonAutoCreator::new();
    if let Err(e) = season_creator.ensure_current_year_seasons(&mut conn) {
        eprintln!("Error auto-creating seasons: {}", e);
    }

    let all_seasons = seasons::table
        .order(seasons::start_date.asc())
        .load::<Season>(&mut conn)
        .expect("Error loading seasons");

    let mut ctx = tera::Context::new();
    ctx.insert("seasons", &all_seasons);

    Ok(render_template_with_context("seasons/list.html", &mut ctx, &session)?)
}

pub async fn new_season_form(session: Session) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    render_template_with_context("seasons/new.html", &mut ctx, &session)
}

pub async fn create_season(
    session: Session,
    form: web::Form<SeasonForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let new_season = NewSeason {
        name: &form.name,
        start_date: NaiveDate::parse_from_str(&form.start_date, "%Y-%m-%d").unwrap(),
        end_date: NaiveDate::parse_from_str(&form.end_date, "%Y-%m-%d").unwrap(),
    };

    diesel::insert_into(seasons::table)
        .values(&new_season)
        .execute(&mut conn)
        .expect("Error inserting new season");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/seasons/list"))
        .finish())
}

pub async fn auto_create_seasons(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let season_creator = SeasonAutoCreator::new();

    match season_creator.ensure_current_year_seasons(&mut conn) {
        Ok(created_ids) => {
            if created_ids.is_empty() {
                Ok(HttpResponse::Ok().json(serde_json::json!({
                    "success": true,
                    "message": "Seasons already exist for current year",
                    "created_count": 0
                })))
            } else {
                Ok(HttpResponse::Ok().json(serde_json::json!({
                    "success": true,
                    "message": format!("Created {} seasons for current year", created_ids.len()),
                    "created_count": created_ids.len(),
                    "season_ids": created_ids
                })))
            }
        }
        Err(e) => {
            eprintln!("Error auto-creating seasons: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "success": false,
                "message": "Failed to create seasons"
            })))
        }
    }
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/seasons")
            .route("/list", web::get().to(list_seasons))
            .route("/new", web::get().to(new_season_form))
            .route("/create", web::post().to(create_season))
            .route("/auto-create", web::post().to(auto_create_seasons)),
    );
}
