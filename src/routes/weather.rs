use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use serde::{Deserialize, Serialize};

use crate::services::weather_service::WeatherService;
use crate::services::notification_service::NotificationService;
use crate::utils::templates::render_template_with_context;
use crate::utils::auth::is_authenticated;
use crate::DbPool;

#[derive(Deserialize)]
pub struct WeatherLocationForm {
    pub latitude: f64,
    pub longitude: f64,
}

#[derive(Serialize)]
pub struct WeatherResponse {
    pub current: crate::services::weather_service::WeatherData,
    pub forecast: Vec<crate::services::weather_service::WeatherForecast>,
    pub alerts: Vec<crate::services::weather_service::WeatherAlert>,
    pub recommendations: Vec<String>,
}

/// Get weather dashboard
pub async fn weather_dashboard(session: Session) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    render_template_with_context("weather/dashboard.html", &mut ctx, &session)
}

/// Get current weather data for a location
pub async fn get_weather_data(
    session: Session,
    query: web::Query<WeatherLocationForm>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let weather_service = WeatherService::new();
    
    // Get current weather
    let current_weather = match weather_service.get_current_weather(query.latitude, query.longitude).await {
        Ok(weather) => weather,
        Err(e) => {
            eprintln!("Error fetching current weather: {}", e);
            return Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to fetch current weather"
            })));
        }
    };

    // Get forecast
    let forecast = match weather_service.get_forecast(query.latitude, query.longitude).await {
        Ok(forecast) => forecast,
        Err(e) => {
            eprintln!("Error fetching weather forecast: {}", e);
            Vec::new()
        }
    };

    // Get alerts
    let alerts = match weather_service.get_weather_alerts(query.latitude, query.longitude).await {
        Ok(alerts) => alerts,
        Err(e) => {
            eprintln!("Error fetching weather alerts: {}", e);
            Vec::new()
        }
    };

    // Generate care recommendations
    let recommendations = weather_service.generate_weather_care_recommendations(&current_weather, &forecast);

    let response = WeatherResponse {
        current: current_weather,
        forecast,
        alerts,
        recommendations,
    };

    Ok(HttpResponse::Ok().json(response))
}

/// Create weather alert notifications for all users
pub async fn create_weather_alert_notifications(
    session: Session,
    pool: web::Data<DbPool>,
    query: web::Query<WeatherLocationForm>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    // Check if user is admin
    let role = session.get::<String>("role")?.unwrap_or_default();
    if role != "admin" && role != "superadmin" {
        return Ok(HttpResponse::Forbidden().json(serde_json::json!({
            "error": "Insufficient permissions"
        })));
    }

    let weather_service = WeatherService::new();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get weather alerts
    let alerts = match weather_service.get_weather_alerts(query.latitude, query.longitude).await {
        Ok(alerts) => alerts,
        Err(e) => {
            eprintln!("Error fetching weather alerts: {}", e);
            return Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to fetch weather alerts"
            })));
        }
    };

    let mut created_notifications = Vec::new();
    let alerts_count = alerts.len();

    // Create notifications for each alert
    for alert in &alerts {
        match NotificationService::create_weather_alert(&mut conn, &alert.description, alert.severity.as_str()) {
            Ok(notifications) => {
                created_notifications.extend(notifications);
            }
            Err(e) => {
                eprintln!("Error creating weather alert notification: {}", e);
            }
        }
    }

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": format!("Created {} weather alert notifications", created_notifications.len()),
        "alerts_processed": alerts_count
    })))
}

/// Get weather recommendations for plant care
pub async fn get_weather_recommendations(
    session: Session,
    query: web::Query<WeatherLocationForm>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let weather_service = WeatherService::new();
    
    // Get current weather and forecast
    let current_weather = match weather_service.get_current_weather(query.latitude, query.longitude).await {
        Ok(weather) => weather,
        Err(e) => {
            eprintln!("Error fetching current weather: {}", e);
            return Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to fetch current weather"
            })));
        }
    };

    let forecast = match weather_service.get_forecast(query.latitude, query.longitude).await {
        Ok(forecast) => forecast,
        Err(e) => {
            eprintln!("Error fetching weather forecast: {}", e);
            Vec::new()
        }
    };

    // Generate care recommendations
    let recommendations = weather_service.generate_weather_care_recommendations(&current_weather, &forecast);

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "recommendations": recommendations,
        "current_weather": current_weather,
        "forecast_summary": {
            "next_24h_temp_range": forecast.iter().take(8).map(|f| (f.temperature_min, f.temperature_max)).collect::<Vec<_>>(),
            "rain_chance": forecast.iter().take(8).map(|f| f.precipitation_chance).fold(0.0, f64::max),
        }
    })))
}

/// Automatically check weather and create alerts (for scheduled tasks)
pub async fn auto_weather_check(
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    let weather_service = WeatherService::new();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Default location (could be made configurable per user/property)
    let default_lat = 40.7128; // New York City
    let default_lon = -74.0060;

    // Get weather alerts
    let alerts = match weather_service.get_weather_alerts(default_lat, default_lon).await {
        Ok(alerts) => alerts,
        Err(e) => {
            eprintln!("Error fetching weather alerts: {}", e);
            return Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to fetch weather alerts"
            })));
        }
    };

    let mut created_notifications = 0;
    let alerts_count = alerts.len();

    // Create notifications for severe alerts only
    for alert in &alerts {
        if matches!(alert.severity, crate::services::weather_service::AlertSeverity::Severe | crate::services::weather_service::AlertSeverity::Extreme) {
            match NotificationService::create_weather_alert(&mut conn, &alert.description, alert.severity.as_str()) {
                Ok(notifications) => {
                    created_notifications += notifications.len();
                }
                Err(e) => {
                    eprintln!("Error creating weather alert notification: {}", e);
                }
            }
        }
    }

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "alerts_checked": alerts_count,
        "notifications_created": created_notifications
    })))
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/weather")
            .route("/dashboard", web::get().to(weather_dashboard))
            .route("/data", web::get().to(get_weather_data))
            .route("/recommendations", web::get().to(get_weather_recommendations))
            .route("/create_alerts", web::post().to(create_weather_alert_notifications))
    );
    
    // API route for automated weather checking
    cfg.route("/api/weather/auto_check", web::post().to(auto_weather_check));
}
