use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WeatherData {
    pub temperature: f64,
    pub humidity: f64,
    pub pressure: f64,
    pub wind_speed: f64,
    pub wind_direction: f64,
    pub description: String,
    pub icon: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct WeatherAlert {
    pub id: String,
    pub title: String,
    pub description: String,
    pub severity: AlertSeverity,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub areas: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Minor,
    Moderate,
    Severe,
    Extreme,
}

impl AlertSeverity {
    pub fn as_str(&self) -> &'static str {
        match self {
            AlertSeverity::Minor => "minor",
            AlertSeverity::Moderate => "moderate", 
            AlertSeverity::Severe => "severe",
            AlertSeverity::Extreme => "extreme",
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WeatherForecast {
    pub date: DateTime<Utc>,
    pub temperature_min: f64,
    pub temperature_max: f64,
    pub humidity: f64,
    pub precipitation_chance: f64,
    pub precipitation_amount: f64,
    pub wind_speed: f64,
    pub description: String,
    pub icon: String,
}

pub struct WeatherService {
    api_key: Option<String>,
    base_url: String,
}

impl WeatherService {
    pub fn new() -> Self {
        Self {
            api_key: std::env::var("OPENWEATHER_API_KEY").ok(),
            base_url: "https://api.openweathermap.org/data/2.5".to_string(),
        }
    }

    /// Get current weather for a location
    pub async fn get_current_weather(&self, lat: f64, lon: f64) -> Result<WeatherData, Box<dyn std::error::Error>> {
        if self.api_key.is_none() {
            return self.get_mock_weather_data();
        }

        let api_key = self.api_key.as_ref().unwrap();
        let url = format!(
            "{}/weather?lat={}&lon={}&appid={}&units=metric",
            self.base_url, lat, lon, api_key
        );

        let response = reqwest::get(&url).await?;
        let data: serde_json::Value = response.json().await?;

        Ok(WeatherData {
            temperature: data["main"]["temp"].as_f64().unwrap_or(20.0),
            humidity: data["main"]["humidity"].as_f64().unwrap_or(50.0),
            pressure: data["main"]["pressure"].as_f64().unwrap_or(1013.0),
            wind_speed: data["wind"]["speed"].as_f64().unwrap_or(0.0),
            wind_direction: data["wind"]["deg"].as_f64().unwrap_or(0.0),
            description: data["weather"][0]["description"].as_str().unwrap_or("clear sky").to_string(),
            icon: data["weather"][0]["icon"].as_str().unwrap_or("01d").to_string(),
            timestamp: Utc::now(),
        })
    }

    /// Get weather forecast for the next 5 days
    pub async fn get_forecast(&self, lat: f64, lon: f64) -> Result<Vec<WeatherForecast>, Box<dyn std::error::Error>> {
        if self.api_key.is_none() {
            return self.get_mock_forecast_data();
        }

        let api_key = self.api_key.as_ref().unwrap();
        let url = format!(
            "{}/forecast?lat={}&lon={}&appid={}&units=metric",
            self.base_url, lat, lon, api_key
        );

        let response = reqwest::get(&url).await?;
        let data: serde_json::Value = response.json().await?;

        let mut forecasts = Vec::new();
        
        if let Some(list) = data["list"].as_array() {
            for item in list.iter().take(40) { // 5 days * 8 forecasts per day (3-hour intervals)
                let forecast = WeatherForecast {
                    date: DateTime::parse_from_str(
                        item["dt_txt"].as_str().unwrap_or("2024-01-01 00:00:00"),
                        "%Y-%m-%d %H:%M:%S"
                    ).unwrap_or_default().with_timezone(&Utc),
                    temperature_min: item["main"]["temp_min"].as_f64().unwrap_or(15.0),
                    temperature_max: item["main"]["temp_max"].as_f64().unwrap_or(25.0),
                    humidity: item["main"]["humidity"].as_f64().unwrap_or(50.0),
                    precipitation_chance: item["pop"].as_f64().unwrap_or(0.0) * 100.0,
                    precipitation_amount: item["rain"]["3h"].as_f64().unwrap_or(0.0),
                    wind_speed: item["wind"]["speed"].as_f64().unwrap_or(0.0),
                    description: item["weather"][0]["description"].as_str().unwrap_or("clear sky").to_string(),
                    icon: item["weather"][0]["icon"].as_str().unwrap_or("01d").to_string(),
                };
                forecasts.push(forecast);
            }
        }

        Ok(forecasts)
    }

    /// Get weather alerts for a location
    pub async fn get_weather_alerts(&self, lat: f64, lon: f64) -> Result<Vec<WeatherAlert>, Box<dyn std::error::Error>> {
        if self.api_key.is_none() {
            return self.get_mock_alert_data();
        }

        let api_key = self.api_key.as_ref().unwrap();
        let url = format!(
            "{}/onecall?lat={}&lon={}&appid={}&exclude=minutely,hourly,daily",
            self.base_url, lat, lon, api_key
        );

        let response = reqwest::get(&url).await?;
        let data: serde_json::Value = response.json().await?;

        let mut alerts = Vec::new();
        
        if let Some(alerts_data) = data["alerts"].as_array() {
            for alert in alerts_data {
                let severity = match alert["tags"][0].as_str().unwrap_or("minor") {
                    "extreme" => AlertSeverity::Extreme,
                    "severe" => AlertSeverity::Severe,
                    "moderate" => AlertSeverity::Moderate,
                    _ => AlertSeverity::Minor,
                };

                let weather_alert = WeatherAlert {
                    id: alert["sender_name"].as_str().unwrap_or("unknown").to_string(),
                    title: alert["event"].as_str().unwrap_or("Weather Alert").to_string(),
                    description: alert["description"].as_str().unwrap_or("").to_string(),
                    severity,
                    start_time: DateTime::from_timestamp(alert["start"].as_i64().unwrap_or(0), 0)
                        .unwrap_or_default(),
                    end_time: alert["end"].as_i64().map(|ts| DateTime::from_timestamp(ts, 0).unwrap_or_default()),
                    areas: vec!["General Area".to_string()],
                };
                alerts.push(weather_alert);
            }
        }

        Ok(alerts)
    }

    /// Generate plant care recommendations based on weather
    pub fn generate_weather_care_recommendations(&self, weather: &WeatherData, forecast: &[WeatherForecast]) -> Vec<String> {
        let mut recommendations = Vec::new();

        // Temperature-based recommendations
        if weather.temperature > 30.0 {
            recommendations.push("🌡️ High temperature alert: Increase watering frequency and provide shade for sensitive plants".to_string());
        } else if weather.temperature < 5.0 {
            recommendations.push("❄️ Low temperature alert: Protect tender plants from frost and reduce watering".to_string());
        }

        // Humidity-based recommendations
        if weather.humidity > 80.0 {
            recommendations.push("💧 High humidity: Watch for fungal diseases and ensure good air circulation".to_string());
        } else if weather.humidity < 30.0 {
            recommendations.push("🏜️ Low humidity: Increase watering and consider misting for humidity-loving plants".to_string());
        }

        // Wind-based recommendations
        if weather.wind_speed > 15.0 {
            recommendations.push("💨 Strong winds: Stake tall plants and protect delicate foliage".to_string());
        }

        // Forecast-based recommendations
        let rain_forecast = forecast.iter().take(8).any(|f| f.precipitation_chance > 70.0);
        if rain_forecast {
            recommendations.push("🌧️ Rain expected: Reduce watering schedule and ensure good drainage".to_string());
        }

        let hot_forecast = forecast.iter().take(8).any(|f| f.temperature_max > 35.0);
        if hot_forecast {
            recommendations.push("🔥 Heat wave approaching: Prepare shade cloth and increase mulching".to_string());
        }

        recommendations
    }

    // Mock data for development/testing when API key is not available
    fn get_mock_weather_data(&self) -> Result<WeatherData, Box<dyn std::error::Error>> {
        Ok(WeatherData {
            temperature: 22.5,
            humidity: 65.0,
            pressure: 1015.0,
            wind_speed: 3.2,
            wind_direction: 180.0,
            description: "partly cloudy".to_string(),
            icon: "02d".to_string(),
            timestamp: Utc::now(),
        })
    }

    fn get_mock_forecast_data(&self) -> Result<Vec<WeatherForecast>, Box<dyn std::error::Error>> {
        let mut forecasts = Vec::new();
        let base_time = Utc::now();
        
        for i in 0..40 {
            let forecast = WeatherForecast {
                date: base_time + chrono::Duration::hours(i * 3),
                temperature_min: 15.0 + (i as f64 * 0.5) % 10.0,
                temperature_max: 25.0 + (i as f64 * 0.3) % 8.0,
                humidity: 50.0 + (i as f64 * 2.0) % 30.0,
                precipitation_chance: (i as f64 * 5.0) % 100.0,
                precipitation_amount: if i % 8 == 0 { 2.5 } else { 0.0 },
                wind_speed: 2.0 + (i as f64 * 0.2) % 5.0,
                description: if i % 4 == 0 { "rain".to_string() } else { "clear sky".to_string() },
                icon: if i % 4 == 0 { "10d".to_string() } else { "01d".to_string() },
            };
            forecasts.push(forecast);
        }
        
        Ok(forecasts)
    }

    fn get_mock_alert_data(&self) -> Result<Vec<WeatherAlert>, Box<dyn std::error::Error>> {
        // Return empty alerts for mock data
        Ok(Vec::new())
    }
}
