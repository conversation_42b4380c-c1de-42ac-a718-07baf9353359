/* Garden Planner Web Application - Material 3 Expressive Design */

/* Material 3 Component Extensions with Cross-Browser Support */
.material-card {
    @apply bg-white dark:bg-surface-800 rounded-lg shadow-elevation-2 border border-surface-200 dark:border-surface-700;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.material-card-elevated {
    @apply bg-white dark:bg-surface-800 rounded-lg shadow-elevation-3 border border-surface-200 dark:border-surface-700;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.material-button-filled {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-full shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    white-space: nowrap;
}

.material-button-outlined {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 font-medium py-3 px-6 rounded-full transition-all duration-200 text-label-large;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    white-space: nowrap;
}

.material-button-text {
    @apply text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 font-medium py-3 px-6 rounded-full transition-all duration-200 text-label-large;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: transparent;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    white-space: nowrap;
}

.material-fab {
    @apply bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-elevation-3 hover:shadow-elevation-4 transition-all duration-200 flex items-center justify-center;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    cursor: pointer;
}

/* Material 3 Form Components */
.material-input {
    @apply w-full px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-surface-800 dark:text-surface-100 text-body-large transition-all duration-200;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    font-family: inherit;
}

.material-label {
    @apply block text-label-large font-medium text-surface-700 dark:text-surface-300 mb-2;
}

.material-select {
    @apply w-full px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-surface-800 dark:text-surface-100 text-body-large transition-all duration-200;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    font-family: inherit;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23525252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.material-textarea {
    @apply w-full px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-surface-800 dark:text-surface-100 text-body-large transition-all duration-200 resize-vertical;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    font-family: inherit;
    min-height: 100px;
}

/* Drawing tools specific styles */
.tool-button {
    @apply px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg text-label-large font-medium text-surface-700 dark:text-surface-200 bg-surface-50 dark:bg-surface-800 hover:bg-surface-100 dark:hover:bg-surface-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200;
}

.tool-button.active {
    @apply bg-primary-600 text-white border-primary-600 shadow-elevation-2;
}

.canvas-container {
    @apply relative border-2 border-surface-300 dark:border-surface-600 rounded-lg overflow-hidden shadow-elevation-1;
    min-height: 400px;
}

/* Button styles */
.btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

.btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

.btn-success {
    @apply bg-tertiary-600 hover:bg-tertiary-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

.btn-danger {
    @apply bg-error-600 hover:bg-error-700 text-white font-medium py-3 px-6 rounded-lg shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 text-label-large;
}

/* Form styles (legacy compatibility) */
.form-input {
    @apply w-full px-4 py-3 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-surface-800 dark:text-surface-100 text-body-large transition-all duration-200;
}

.form-label {
    @apply block text-label-large font-medium text-surface-700 dark:text-surface-300 mb-2;
}

/* Navigation styles */
.nav-link {
    @apply text-white hover:text-primary-200 px-3 py-2 rounded-md text-body-medium font-medium transition-colors;
}

.nav-link.active {
    @apply bg-primary-700 text-white shadow-elevation-1;
}

/* Card styles */
.card {
    @apply bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-2 border border-surface-200 dark:border-surface-700;
}

.card-header {
    @apply px-6 py-4 border-b border-surface-200 dark:border-surface-600;
}

.card-body {
    @apply px-6 py-4;
}

/* Notification styles */
.notification-item {
    @apply px-4 py-3 hover:bg-surface-100 dark:hover:bg-surface-700 border-b border-surface-200 dark:border-surface-600 cursor-pointer transition-colors;
}

/* Statistics dashboard */
.stat-card {
    @apply bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-2 p-6 text-center;
}

.stat-value {
    @apply text-display-small font-normal text-surface-900 dark:text-surface-100;
}

.stat-label {
    @apply text-body-medium text-surface-600 dark:text-surface-400;
}

/* Responsive design */
@media (max-width: 768px) {
    .canvas-container {
        min-height: 300px;
    }
    
    .tool-button {
        @apply px-2 py-1 text-xs;
    }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
    .canvas-container {
        @apply border-surface-600;
    }
}

/* Animation utilities */
.transition-all {
    transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
}

.transition-colors {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    -webkit-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    -moz-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    -o-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* Menu overflow and positioning fixes */
.dropdown-menu {
    max-height: 80vh;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: rgb(90 126 90) rgb(244 244 245);
}

.dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: rgb(244 244 245);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: rgb(90 126 90);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: rgb(72 101 72);
}

/* Navigation menu fixes */
.nav-menu {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nav-menu-item {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    min-width: 0;
    flex-shrink: 1;
}

.nav-menu-item span {
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Firefox-specific fixes */
@-moz-document url-prefix() {
    .material-button-filled,
    .material-button-outlined,
    .material-button-text {
        -moz-appearance: none;
        border: none;
        outline: none;
    }

    .material-input,
    .material-select,
    .material-textarea {
        -moz-appearance: none;
    }

    /* Fix Firefox scrollbar styling */
    * {
        scrollbar-width: thin;
        scrollbar-color: rgb(90 126 90) rgb(244 244 245);
    }

    .dark * {
        scrollbar-color: rgb(90 126 90) rgb(63 63 70);
    }

    /* Firefox dropdown menu fixes */
    .dropdown-menu {
        scrollbar-width: thin;
        scrollbar-color: rgb(90 126 90) rgb(244 244 245);
    }

    .dark .dropdown-menu {
        scrollbar-color: rgb(90 126 90) rgb(63 63 70);
    }
}

/* Grid and drawing specific styles */
.grid-toggle-btn.active {
    background-color: rgb(90 126 90) !important; /* sage primary-500 */
    color: white !important;
    border: 2px solid rgb(90 126 90) !important;
}

/* Wishlist styles */
.wishlist-item {
    @apply flex items-center justify-between p-4 bg-surface-100 dark:bg-surface-700 rounded-lg border border-surface-200 dark:border-surface-600 shadow-elevation-1;
}

/* Property visualization */
.property-shape {
    fill: rgba(90, 126, 90, 0.2); /* sage primary-500 with opacity */
    stroke: rgba(90, 126, 90, 0.8);
    stroke-width: 2;
}

.growing-area-shape {
    fill: rgba(116, 137, 116, 0.3); /* sage secondary-500 with opacity */
    stroke: rgba(116, 137, 116, 0.8);
    stroke-width: 2;
}
