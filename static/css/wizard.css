/* Material 3 Wizard styles */
.canvas-container {
    position: relative;
    width: 100%;
    height: 400px;
    background-color: rgb(250 250 250); /* surface-50 */
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); /* elevation-1 */
    margin-bottom: 1.5rem;
    border: 1px solid rgb(228 228 231); /* surface-200 */
}

.dark .canvas-container {
    background-color: rgb(39 39 42); /* surface-800 */
    border-color: rgb(82 82 91); /* surface-600 */
}

.drawing-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: rgb(244 244 245); /* surface-100 */
    border-radius: 12px;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); /* elevation-1 */
    border: 1px solid rgb(228 228 231); /* surface-200 */
}

.dark .drawing-tools {
    background-color: rgb(63 63 70); /* surface-700 */
    border-color: rgb(82 82 91); /* surface-600 */
}

.tool-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background-color: rgb(250 250 250); /* surface-50 */
    border: 1px solid rgb(212 212 216); /* surface-300 */
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); /* elevation-1 */
}

.dark .tool-button {
    background-color: rgb(39 39 42); /* surface-800 */
    border-color: rgb(82 82 91); /* surface-600 */
}

.tool-button:hover {
    background-color: rgb(244 244 245); /* surface-100 */
    transform: translateY(-1px);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15); /* elevation-2 */
}

.dark .tool-button:hover {
    background-color: rgb(63 63 70); /* surface-700 */
}

.tool-button .icon {
    font-size: 1.5rem;
}

.tool-button.active {
    background-color: rgb(90 126 90); /* sage primary-500 */
    color: white;
    border-color: rgb(72 101 72); /* sage primary-600 */
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15); /* elevation-2 */
}

.dark .tool-button.active {
    background-color: rgb(72 101 72); /* sage primary-600 */
    border-color: rgb(58 82 58); /* sage primary-700 */
}

#dimensionPopup {
    backdrop-filter: blur(4px);
}

#dimensionPopupInner {
    max-width: 90%;
    width: 350px;
    transform: translate(-50%, -50%);
    animation: popup-fade-in 0.3s ease-out;
}

@keyframes popup-fade-in {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.btn-success {
    background-color: rgb(90 130 90); /* sage tertiary-500 */
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); /* elevation-1 */
}

.btn-success:hover {
    background-color: rgb(70 104 70); /* sage tertiary-600 */
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15); /* elevation-2 */
}

.btn-danger {
    background-color: rgb(239 68 68); /* error-500 */
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); /* elevation-1 */
}

.btn-danger:hover {
    background-color: rgb(220 38 38); /* error-600 */
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15); /* elevation-2 */
}

.btn-secondary {
    background-color: rgb(116 137 116); /* sage secondary-500 */
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); /* elevation-1 */
}

.btn-secondary:hover {
    background-color: rgb(93 111 93); /* sage secondary-600 */
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15); /* elevation-2 */
}

.btn-primary {
    background-color: rgb(90 126 90); /* sage primary-500 */
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15); /* elevation-1 */
}

.btn-primary:hover {
    background-color: rgb(72 101 72); /* sage primary-600 */
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15); /* elevation-2 */
}

/* Wizard progress indicator */
.wizard-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.wizard-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e5e7eb;
    transform: translateY(-50%);
    z-index: 0;
}

.dark .wizard-progress::before {
    background-color: #4b5563;
}

.wizard-step {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.wizard-step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #6b7280;
}

.dark .wizard-step-circle {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #9ca3af;
}

.wizard-step.active .wizard-step-circle {
    background-color: #3b82f6;
    border-color: #2563eb;
    color: white;
}

.dark .wizard-step.active .wizard-step-circle {
    background-color: #2563eb;
    border-color: #1d4ed8;
}

.wizard-step.completed .wizard-step-circle {
    background-color: #10b981;
    border-color: #059669;
    color: white;
}

.dark .wizard-step.completed .wizard-step-circle {
    background-color: #059669;
    border-color: #047857;
}

.wizard-step-label {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

.dark .wizard-step-label {
    color: #9ca3af;
}

.wizard-step.active .wizard-step-label {
    color: #3b82f6;
    font-weight: 600;
}

.dark .wizard-step.active .wizard-step-label {
    color: #60a5fa;
}

.wizard-step.completed .wizard-step-label {
    color: #10b981;
}

.dark .wizard-step.completed .wizard-step-label {
    color: #34d399;
}
