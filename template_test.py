#!/usr/bin/env python3
"""
Template and Design Consistency Test
Tests all templates for proper rendering and Material 3 design consistency
"""

import requests
import re
import random
import string

BASE_URL = "http://127.0.0.1:8080"

def random_string(length=8):
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def get_csrf_token(html_content):
    """Extract CSRF token from HTML"""
    match = re.search(r'name="csrf_token" value="([^"]+)"', html_content)
    return match.group(1) if match else None

def check_template_elements(html_content, page_name):
    """Check for required template elements"""
    issues = []
    
    # Check for base template elements
    if '<nav' not in html_content:
        issues.append("Missing navigation bar")
    
    if 'theme-toggle' not in html_content:
        issues.append("Missing theme toggle")
    
    # Check for Material 3 design system
    material3_classes = ['material-', 'bg-primary', 'text-primary', 'bg-surface', 'text-surface', 'material-button', 'material-card']
    has_material3_theme = any(cls in html_content for cls in material3_classes)
    if not has_material3_theme:
        issues.append("Missing Material 3 styling")

    # Check for Tailwind CSS classes
    tailwind_classes = ['container', 'mx-auto', 'px-', 'py-', 'flex', 'items-center']
    has_tailwind = any(cls in html_content for cls in tailwind_classes)
    if not has_tailwind:
        issues.append("Missing Tailwind CSS styling")
    
    # Check for proper form structure
    if '<form' in html_content:
        if 'csrf_token' not in html_content:
            issues.append("Form missing CSRF protection")
    
    # Check for error handling
    if 'error' in page_name.lower() or '404' in page_name:
        error_indicators = ['404', 'Page Not Found', 'error', 'not found', 'min-h-screen']
        has_error_styling = any(indicator in html_content for indicator in error_indicators)
        if not has_error_styling:
            issues.append("Error page missing proper error styling")
    
    return issues

def test_authenticated_pages():
    """Test pages that require authentication"""
    print("\n🔐 Testing Authenticated Pages")
    print("-" * 40)
    
    session = requests.Session()
    username = f"testuser_{random_string()}"
    password = "testpass123"
    
    # Get CSRF token for registration
    reg_response = session.get(f"{BASE_URL}/auth/register")
    csrf_token = get_csrf_token(reg_response.text)

    # Register and login
    register_data = {
        'username': username,
        'email': f"{username}@test.com",
        'password': password,
        'password_confirm': password,
        'csrf_token': csrf_token
    }

    response = session.post(f"{BASE_URL}/auth/register", data=register_data)
    if response.status_code not in [200, 302]:
        print(f"❌ Registration failed - Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        return

    # Check if we're on the household creation page (successful registration)
    if "Create Household" in response.text or "household" in response.text.lower():
        print("✅ Registration successful (redirected to household creation)")
    else:
        print(f"❌ Registration failed - unexpected response")
        return
    
    # Get CSRF token for login
    login_response = session.get(f"{BASE_URL}/auth/login")
    csrf_token = get_csrf_token(login_response.text)

    login_data = {
        'username': username,
        'password': password,
        'csrf_token': csrf_token
    }
    response = session.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code not in [200, 302]:
        print(f"❌ Login failed - Status: {response.status_code}")
        return
    
    print("✅ Authentication successful")
    
    # Test authenticated pages
    auth_pages = [
        "/wishlist/plants",
        "/wishlist/seeds", 
        "/notifications/list",
        "/profile",
        "/households",
        "/property/wizard"
    ]
    
    for page in auth_pages:
        try:
            response = session.get(f"{BASE_URL}{page}")
            if response.status_code == 200:
                issues = check_template_elements(response.text, page)
                if issues:
                    print(f"⚠️  {page} - Issues: {', '.join(issues)}")
                else:
                    print(f"✅ {page} - Template OK")
            else:
                print(f"❌ {page} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {page} - Error: {e}")

def test_public_pages():
    """Test pages that don't require authentication"""
    print("🌐 Testing Public Pages")
    print("-" * 40)
    
    public_pages = [
        "/",
        "/auth/login",
        "/auth/register", 
        "/plants/list",
        "/seeds/list",
        "/property",
        "/seasons/list",
        "/season_plans",
        "/admin"
    ]
    
    for page in public_pages:
        try:
            response = requests.get(f"{BASE_URL}{page}")
            if response.status_code == 200:
                issues = check_template_elements(response.text, page)
                if issues:
                    print(f"⚠️  {page} - Issues: {', '.join(issues)}")
                else:
                    print(f"✅ {page} - Template OK")
            else:
                print(f"❌ {page} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {page} - Error: {e}")

def test_error_pages():
    """Test error page templates"""
    print("\n🚫 Testing Error Pages")
    print("-" * 40)
    
    error_urls = [
        "/nonexistent-page",
        "/admin/nonexistent",
        "/api/nonexistent"
    ]
    
    for url in error_urls:
        try:
            response = requests.get(f"{BASE_URL}{url}")
            if response.status_code == 404:
                issues = check_template_elements(response.text, "404_error")
                if issues:
                    print(f"⚠️  404 page - Issues: {', '.join(issues)}")
                else:
                    print(f"✅ 404 page - Template OK")
                break
        except Exception as e:
            print(f"❌ Error page test failed: {e}")

def test_api_endpoints():
    """Test API endpoints return proper JSON"""
    print("\n🔌 Testing API Endpoints")
    print("-" * 40)
    
    # Test unauthenticated API access
    try:
        response = requests.get(f"{BASE_URL}/api/notifications/recent")
        if response.status_code == 401:
            try:
                data = response.json()
                print("✅ API returns proper JSON for unauthorized access")
            except:
                print("⚠️  API should return JSON for unauthorized access")
        else:
            print(f"⚠️  API unexpected status: {response.status_code}")
    except Exception as e:
        print(f"❌ API test failed: {e}")

def main():
    print("🎨 Garden Planner Template & Design Test")
    print("=" * 50)
    
    # Check server health
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ Server not responding")
            return
    except:
        print("❌ Cannot connect to server")
        return
    
    print("✅ Server is responding")
    
    test_public_pages()
    test_authenticated_pages()
    test_error_pages()
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("🎯 Template test completed!")
    print("Check results above for any design consistency issues.")

if __name__ == "__main__":
    main()
