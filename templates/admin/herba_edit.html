{% extends "base.html" %}
{% block title %}Edit HerbaDB Entry{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-headline-medium font-normal text-surface-900 dark:text-surface-100">Edit HerbaDB Entry</h1>
            <p class="text-body-medium text-surface-600 dark:text-surface-400 mt-2">Update botanical information for {{ herba_plant.common_name }}</p>
        </div>
        <a href="/admin/herba-db" class="material-button-outlined">
            Back to Database
        </a>
    </div>

    <div class="material-card-elevated p-8">
        <form method="post" action="/admin/herba-db/{{ herba_plant.id }}/edit" class="space-y-6">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="latin_name" class="material-label">Latin Name *</label>
                    <input type="text" id="latin_name" name="latin_name" value="{{ herba_plant.latin_name }}" required class="material-input">
                </div>

                <div>
                    <label for="common_name" class="material-label">Common Name *</label>
                    <input type="text" id="common_name" name="common_name" value="{{ herba_plant.common_name }}" required class="material-input">
                </div>
            </div>

            <!-- Taxonomic Information -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="family" class="material-label">Family</label>
                    <input type="text" id="family" name="family" value="{{ herba_plant.family | default(value='') }}" class="material-input">
                </div>

                <div>
                    <label for="genus" class="material-label">Genus</label>
                    <input type="text" id="genus" name="genus" value="{{ herba_plant.genus | default(value='') }}" class="material-input">
                </div>

                <div>
                    <label for="species" class="material-label">Species</label>
                    <input type="text" id="species" name="species" value="{{ herba_plant.species | default(value='') }}" class="material-input">
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="material-label">Description</label>
                <textarea id="description" name="description" rows="4" class="material-input">{{ herba_plant.description | default(value='') }}</textarea>
            </div>

            <!-- Growing Conditions -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="growth_habit" class="material-label">Growth Habit</label>
                    <select id="growth_habit" name="growth_habit" class="material-select">
                        <option value="">Select growth habit</option>
                        <option value="annual" {% if herba_plant.growth_habit == "annual" %}selected{% endif %}>Annual</option>
                        <option value="biennial" {% if herba_plant.growth_habit == "biennial" %}selected{% endif %}>Biennial</option>
                        <option value="perennial" {% if herba_plant.growth_habit == "perennial" %}selected{% endif %}>Perennial</option>
                        <option value="shrub" {% if herba_plant.growth_habit == "shrub" %}selected{% endif %}>Shrub</option>
                        <option value="tree" {% if herba_plant.growth_habit == "tree" %}selected{% endif %}>Tree</option>
                    </select>
                </div>

                <div>
                    <label for="hardiness_zone" class="material-label">Hardiness Zone</label>
                    <input type="text" id="hardiness_zone" name="hardiness_zone" value="{{ herba_plant.hardiness_zone | default(value='') }}" class="material-input" placeholder="e.g., 5-9">
                </div>
            </div>

            <!-- Environmental Requirements -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="sun_requirements" class="material-label">Sun Requirements</label>
                    <select id="sun_requirements" name="sun_requirements" class="material-select">
                        <option value="">Select sun requirements</option>
                        <option value="full_sun" {% if herba_plant.sun_requirements == "full_sun" %}selected{% endif %}>Full Sun</option>
                        <option value="partial_sun" {% if herba_plant.sun_requirements == "partial_sun" %}selected{% endif %}>Partial Sun</option>
                        <option value="partial_shade" {% if herba_plant.sun_requirements == "partial_shade" %}selected{% endif %}>Partial Shade</option>
                        <option value="full_shade" {% if herba_plant.sun_requirements == "full_shade" %}selected{% endif %}>Full Shade</option>
                    </select>
                </div>

                <div>
                    <label for="water_requirements" class="material-label">Water Requirements</label>
                    <select id="water_requirements" name="water_requirements" class="material-select">
                        <option value="">Select water requirements</option>
                        <option value="low" {% if herba_plant.water_requirements == "low" %}selected{% endif %}>Low</option>
                        <option value="medium" {% if herba_plant.water_requirements == "medium" %}selected{% endif %}>Medium</option>
                        <option value="high" {% if herba_plant.water_requirements == "high" %}selected{% endif %}>High</option>
                    </select>
                </div>

                <div>
                    <label for="soil_type" class="material-label">Soil Type</label>
                    <input type="text" id="soil_type" name="soil_type" value="{{ herba_plant.soil_type | default(value='') }}" class="material-input" placeholder="e.g., well-drained, sandy">
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-surface-200 dark:border-surface-700">
                <a href="/admin/herba-db" class="material-button-outlined">
                    Cancel
                </a>
                <button type="submit" class="material-button-filled">
                    Update Entry
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
