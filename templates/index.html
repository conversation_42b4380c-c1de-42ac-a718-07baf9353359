{% extends "base.html" %}
{% block title %}Home - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Hero Section -->
    <div class="text-center py-16">
        <h1 class="text-display-large font-normal text-surface-900 dark:text-surface-100 mb-6">
            Welcome to Garden Planner{% if user_context.username %}, {{ user_context.username }}{% endif %}!
            <span class="material-icons text-6xl align-middle ml-2">eco</span>
        </h1>
        <p class="text-headline-small text-surface-600 dark:text-surface-300 mb-8 max-w-4xl mx-auto">
            Plan, manage, and optimize your garden with our comprehensive gardening tools.
            Track plants, manage seasons, and get personalized notifications for your garden care.
        </p>

        {% if not user_context.is_authenticated %}
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/auth/register" class="material-button-filled">
                    Get Started
                </a>
                <a href="/auth/login" class="material-button-outlined">
                    Login
                </a>
            </div>
        {% endif %}
    </div>

    {% if user_context.is_authenticated %}
        <!-- Statistics Dashboard -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-12">
            <div class="stat-card">
                <div class="stat-value text-primary-600 dark:text-primary-400">{{ user_properties | default(value=0) }}</div>
                <div class="stat-label">My Properties</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-secondary-600 dark:text-secondary-400">{{ shared_properties | default(value=0) }}</div>
                <div class="stat-label">Shared Properties</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-tertiary-600 dark:text-tertiary-400">{{ user_households | default(value=0) }}</div>
                <div class="stat-label">Households</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-primary-700 dark:text-primary-300">{{ total_plants | default(value=0) }}</div>
                <div class="stat-label">Plants Available</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-secondary-700 dark:text-secondary-300">{{ total_seeds | default(value=0) }}</div>
                <div class="stat-label">Seeds Tracked</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-tertiary-700 dark:text-tertiary-300">{{ user_wishlist | default(value=0) }}</div>
                <div class="stat-label">Wishlist Items</div>
            </div>
        </div>

        <!-- Quick Actions for Authenticated Users -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-primary-600 dark:text-primary-400">local_florist</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Manage Plants</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Add and manage your plant database with detailed information.</p>
                <a href="/plants/list" class="text-primary-700 hover:text-primary-900 dark:text-primary-300 dark:hover:text-primary-100 font-medium text-label-large">View Plants →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-400">home</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Properties</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Design and manage your garden properties and growing areas.</p>
                <a href="/property" class="text-secondary-700 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-secondary-100 font-medium text-label-large">View Properties →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-tertiary-100 dark:bg-tertiary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-400">event</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Season Plans</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Plan your growing seasons and optimize crop rotations.</p>
                <a href="/season_plans" class="text-tertiary-700 hover:text-tertiary-900 dark:text-tertiary-300 dark:hover:text-tertiary-100 font-medium text-label-large">View Plans →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-primary-600 dark:text-primary-400">inventory</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Seeds & Inventory</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Track your seed inventory and planting schedules.</p>
                <a href="/seeds/list" class="text-primary-700 hover:text-primary-900 dark:text-primary-300 dark:hover:text-primary-100 font-medium text-label-large">View Seeds →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-400">groups</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Households</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Manage and share your garden with household members.</p>
                <a href="/households" class="text-secondary-700 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-secondary-100 font-medium text-label-large">View Households →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-tertiary-100 dark:bg-tertiary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-400">notifications</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Notifications</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Stay on top of watering, fertilizing, and care schedules.</p>
                <a href="/notifications/list" class="text-tertiary-700 hover:text-tertiary-900 dark:text-tertiary-300 dark:hover:text-tertiary-100 font-medium text-label-large">View Notifications →</a>
            </div>
        </div>
    {% else %}
        <!-- Features for Non-Authenticated Users -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16">
            <div class="text-center">
                <div class="w-20 h-20 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="material-icons text-3xl text-primary-600 dark:text-primary-400">local_florist</span>
                </div>
                <h3 class="text-headline-small font-normal text-surface-900 dark:text-surface-100 mb-4">Plant Database</h3>
                <p class="text-body-large text-surface-600 dark:text-surface-300">Comprehensive plant information with growing requirements and care instructions.</p>
            </div>

            <div class="text-center">
                <div class="w-20 h-20 bg-secondary-100 dark:bg-secondary-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="material-icons text-3xl text-secondary-600 dark:text-secondary-400">event</span>
                </div>
                <h3 class="text-headline-small font-normal text-surface-900 dark:text-surface-100 mb-4">Season Planning</h3>
                <p class="text-body-large text-surface-600 dark:text-surface-300">Plan your garden seasons with automated optimization and crop rotation suggestions.</p>
            </div>

            <div class="text-center">
                <div class="w-20 h-20 bg-tertiary-100 dark:bg-tertiary-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="material-icons text-3xl text-tertiary-600 dark:text-tertiary-400">notifications</span>
                </div>
                <h3 class="text-headline-small font-normal text-surface-900 dark:text-surface-100 mb-4">Smart Notifications</h3>
                <p class="text-body-large text-surface-600 dark:text-surface-300">Get personalized reminders for watering, fertilizing, and seasonal garden tasks.</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
