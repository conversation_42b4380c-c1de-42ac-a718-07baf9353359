{% extends "base.html" %}
{% block title %}Register{% endblock %}
{% block content %}
<div class="max-w-md mx-auto">
  <div class="material-card-elevated p-8 mt-16">
    <div class="text-center mb-8">
      <span class="material-icons text-5xl text-primary-600 dark:text-primary-400 mb-4 block">person_add</span>
      <h2 class="text-headline-medium font-normal text-surface-900 dark:text-surface-100">Create an Account</h2>
    </div>

    {% if error %}
    <div class="mb-6 p-4 bg-error-100 dark:bg-error-900 border border-error-400 dark:border-error-700 text-error-700 dark:text-error-200 rounded-lg">
      <div class="flex items-center">
        <span class="material-icons text-lg mr-3">error</span>
        {{ error }}
      </div>
    </div>
    {% endif %}

    <form method="post" action="/auth/register" class="space-y-6">
      <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

      <div>
        <label for="username" class="material-label">Username</label>
        <input type="text" id="username" name="username" required class="material-input">
      </div>

      <div>
        <label for="password" class="material-label">Password</label>
        <input type="password" id="password" name="password" required class="material-input">
      </div>

      <div>
        <label for="password_confirm" class="material-label">Confirm Password</label>
        <input type="password" id="password_confirm" name="password_confirm" required class="material-input">
      </div>

      <button type="submit" class="w-full material-button-filled">
        Register
      </button>
    </form>

    <p class="mt-8 text-center text-body-medium text-surface-600 dark:text-surface-400">
      Already have an account?
      <a href="/auth/login" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
        Login here
      </a>
    </p>
  </div>
</div>
{% endblock %}
